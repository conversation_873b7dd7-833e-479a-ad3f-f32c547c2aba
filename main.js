/**
 * OTA订单处理系统 - 主入口文件 (重构版)
 * 使用新的启动协调器统一管理初始化流程
 * 解决架构混乱问题
 */

// 等待所有模块加载完成后启动应用
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 开始启动OTA订单处理系统...');

    try {
        // 检查核心模块是否已加载
        if (!window.OTA || !window.OTA.container || !window.OTA.ApplicationBootstrap) {
            throw new Error('核心架构模块未正确加载，请检查script标签顺序');
        }

        // 创建启动协调器实例
        const bootstrap = new window.OTA.ApplicationBootstrap();

        // 启动应用
        const result = await bootstrap.start();

        if (result.success) {
            console.log(`✅ OTA系统启动成功，耗时: ${result.duration.toFixed(2)}ms`);

            // 暴露全局应用实例（用于调试和向后兼容）
            window.app = {
                bootstrap,
                container: window.OTA.container,
                serviceLocator: window.OTA.serviceLocator,
                getService: window.OTA.getService,
                version: '2.0.0-refactored',
                startTime: Date.now()
            };

            // 暴露到OTA命名空间
            window.OTA.app = window.app;

        } else {
            throw new Error(`系统启动失败: ${result.error}`);
        }

    } catch (error) {
        console.error('❌ 系统启动失败:', error);

        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #ff4444; color: white; padding: 20px; border-radius: 8px;
            font-family: Arial, sans-serif; z-index: 10000; max-width: 500px;
        `;
        errorDiv.innerHTML = `
            <h3>系统启动失败</h3>
            <p>${error.message}</p>
            <button onclick="location.reload()" style="background: white; color: #ff4444; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                重新加载
            </button>
        `;
        document.body.appendChild(errorDiv);
    }
});

// 注意：LegacyOTAApplication 备用代码已移除 (节省 628 行代码)
// 现在完全使用 ApplicationBootstrap 新架构


// 执行系统健康检查
function performSystemHealthCheck() {
    const logger = window.OTA.logger;
    logger.log('🔍 开始系统健康检查...', 'info');

    const checks = [
        // 核心服务检查
        { name: 'Logger服务', check: () => window.OTA && window.OTA.logger },
        { name: 'AppState服务', check: () => typeof getAppState === 'function' && window.OTA.appState },
        { name: 'Gemini服务', check: () => typeof getGeminiService === 'function' && window.OTA.geminiService },
        { name: 'API服务', check: () => typeof getAPIService === 'function' && window.OTA.apiService },
        { name: 'UI管理器', check: () => typeof getUIManager === 'function' && window.OTA.uiManager },

        // 功能管理器检查
        { name: '图片上传管理器', check: () => typeof getImageUploadManager === 'function' && window.OTA.imageUploadManager },
        { name: '货币转换器', check: () => typeof getCurrencyConverter === 'function' && window.OTA.currencyConverter },
        { name: '多订单管理器', check: () => typeof getMultiOrderManager === 'function' && window.OTA.multiOrderManager },
        { name: '分页服务管理器', check: () => typeof getPagingServiceManager === 'function' && window.OTA.pagingServiceManager },
        { name: '订单历史管理器', check: () => typeof getOrderHistoryManager === 'function' && window.OTA.orderHistoryManager },
        { name: '国际化管理器', check: () => typeof getI18nManager === 'function' && window.OTA.i18nManager },

        // DOM元素检查
        { name: '登录面板', check: () => document.getElementById('loginPanel') },
        { name: '工作区', check: () => document.getElementById('workspace') },
        { name: '订单输入框', check: () => document.getElementById('orderInput') },
        { name: '创建订单按钮', check: () => document.getElementById('createOrderBtn') }
    ];

    let passedChecks = 0;
    let totalChecks = checks.length;

    checks.forEach(({ name, check }) => {
        try {
            const result = check();
            if (result) {
                logger.log(`✅ ${name}: 正常`, 'success');
                passedChecks++;
            } else {
                logger.log(`❌ ${name}: 未找到或未初始化`, 'error');
            }
        } catch (error) {
            logger.log(`❌ ${name}: 检查失败 - ${error.message}`, 'error');
        }
    });

    const healthScore = Math.round((passedChecks / totalChecks) * 100);
    logger.log(`🏥 系统健康检查完成: ${passedChecks}/${totalChecks} 项通过 (${healthScore}%)`,
        healthScore >= 80 ? 'success' : healthScore >= 60 ? 'warning' : 'error');

    // 性能监控信息
    if (window.performance && window.performance.memory) {
        const memory = window.performance.memory;
        logger.log(`💾 内存使用: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB / ${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`, 'info');
    }

    return {
        score: healthScore,
        passed: passedChecks,
        total: totalChecks,
        timestamp: new Date().toISOString()
    };
}

/**
 * 设置监控控制台命令
 * @param {object} logger - Logger实例
 */
function setupMonitoringCommands(logger) {
    // 添加全局监控控制命令
    window.monitoring = {
        /**
         * 显示监控报告
         */
        report: () => {
            return logger.printMonitoringReport();
        },
        
        /**
         * 启用/禁用实时监控
         * @param {boolean} enabled - 是否启用
         */
        setRealTime: (enabled) => {
            logger.monitoring.realTimeConsole = Boolean(enabled);
            logger.log(`实时控制台监控${enabled ? '启用' : '禁用'}`, 'info');
        },
        
        /**
         * 启用/禁用调试模式
         * @param {boolean} enabled - 是否启用
         */
        setDebug: (enabled) => {
            logger.setDebugMode(enabled);
        },
        
        /**
         * 清除监控数据
         */
        clear: () => {
            logger.monitoring.factoryFunctions.clear();
            logger.monitoring.performanceMetrics.clear();
            logger.monitoring.userInteractions = [];
            logger.monitoring.systemStates.clear();
            logger.monitoring.errorTracking.clear();
            logger.log('监控数据已清除', 'info');
        },
        
        /**
         * 获取工厂函数统计
         */
        getFactoryStats: () => {
            return logger.getFactoryFunctionStats();
        },
        
        /**
         * 获取性能统计
         */
        getPerformanceStats: () => {
            return logger.getPerformanceStats();
        },
        
        /**
         * 测试工厂函数性能
         */
        testFactoryFunctions: () => {
            logger.log('🧪 开始工厂函数性能测试...', 'info');
            
            const factoryFunctions = [
                'getAppState',
                'getGeminiService',
                'getAPIService',
                'getImageUploadManager',
                'getCurrencyConverter'
            ];
            
            factoryFunctions.forEach(funcName => {
                if (window[funcName]) {
                    const start = performance.now();
                    try {
                        const result = window[funcName]();
                        const duration = performance.now() - start;
                        logger.log(`${funcName}: ${duration.toFixed(2)}ms`, 'info');
                    } catch (error) {
                        logger.log(`${funcName}: ERROR - ${error.message}`, 'error');
                    }
                }
            });
        },
        
        /**
         * 导出监控数据
         * @param {string} format - 导出格式 (json, csv, txt)
         */
        export: (format = 'json') => {
            const report = logger.getMonitoringReport();
            const data = JSON.stringify(report, null, 2);
            
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `monitoring-report-${new Date().toISOString().slice(0, 19)}.${format}`;
            a.click();
            URL.revokeObjectURL(url);
            
            logger.log('监控报告已导出', 'success');
        }
    };
    
    logger.log('📋 监控控制台命令已设置', 'success', {
        type: 'monitoring_commands_ready',
        commands: [
            'monitoring.report() - 显示监控报告',
            'monitoring.setRealTime(true/false) - 启用/禁用实时监控',
            'monitoring.setDebug(true/false) - 启用/禁用调试模式',
            'monitoring.clear() - 清除监控数据',
            'monitoring.testFactoryFunctions() - 测试工厂函数性能',
            'monitoring.export("json") - 导出监控数据'
        ]
    });
    
    // 在控制台显示使用提示
    console.info(
        '%c🔍 全局监控系统已启动',
        'color: #3498db; font-size: 14px; font-weight: bold;'
    );
    console.info(
        '%c使用 monitoring.report() 查看监控报告',
        'color: #2ecc71; font-size: 12px;'
    );
    console.info(
        '%c使用 monitoring.setDebug(true) 启用调试模式',
        'color: #e67e22; font-size: 12px;'
    );
}