/**
 * CSS变量系统 - 全局变量定义
 * 包含颜色、间距、字体、动画等基础变量
 */

:root {
  /* =================================
     品牌色彩系统 - 基于公司logo #9F299F
     ================================= */
  --color-primary: #9F299F;
  --color-primary-hover: #B84CB8;
  --color-primary-light: #C166C1;
  --color-primary-gradient: linear-gradient(135deg, #9F299F 0%, #B84CB8 50%, #7A1F7A 100%);
  
  /* 辅助色 */
  --color-secondary: #8E8E93;
  --color-secondary-hover: #6B6B70;
  --color-secondary-light: #F0F0F3;
  
  /* 状态色 */
  --color-success: #4CAF50;
  --color-success-light: #E8F5E8;
  --color-success-green: #22c55e;        /* 绿色成功态 */
  --color-success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  --color-success-hover: #45A049;        /* 成功按钮悬停态 */
  
  --color-warning: #FF9800;
  --color-warning-light: #FFF3E0;
  --color-warning-amber: #f59e0b;        /* 琥珀色警告态 */
  --color-warning-yellow: #ffc107;       /* 黄色警告态 */
  --color-warning-hover: #E68900;        /* 警告按钮悬停态 */
  
  --color-error: #F44336;
  --color-error-light: #FFEBEE;
  --color-error-red: #ef4444;            /* 红色错误态 */
  --color-error-gradient: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  --color-error-hover: #DA190B;          /* 错误按钮悬停态 */
  
  --color-info: #2196F3;
  --color-info-light: #E3F2FD;
  --color-info-blue: #3b82f6;            /* 蓝色信息态 */
  --color-info-cyan: #17a2b8;            /* 青色信息态 */
  --color-info-gradient: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  --color-blue-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);     /* 蓝紫渐变 */
  --color-blue-gradient-alt: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%); /* 备用蓝紫渐变 */
  --color-red-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);       /* 红色渐变 */
  --color-red-gradient-alt: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);   /* 备用红色渐变 */
  --color-red-bg-light: #fff5f5;                                                /* 浅红背景 */
  
  /* =================================
     Neumorphism背景色系
     ================================= */
  --color-white: #FFFFFF;
  --color-neu-bg: #F0F0F3;        /* 主背景 */
  --color-neu-bg-secondary: #E6E6EA;  /* 次背景 */
  --color-neu-card: #FAFAFA;      /* 卡片背景 */
  --color-neu-light: #FFFFFF;     /* 高光色 */
  --color-neu-shadow: #D1D1D6;    /* 阴影色 */
  --color-neu-border: #E0E0E3;    /* 边框色 */
  
  /* =================================
     中性色系
     ================================= */
  --color-gray-50: #F8F8FA;
  --color-gray-100: #F0F0F3;
  --color-gray-200: #E6E6EA;
  --color-gray-300: #D1D1D6;
  --color-gray-400: #A8A8B0;
  --color-gray-500: #8E8E93;
  --color-gray-600: #6B6B70;
  --color-gray-700: #48484A;
  --color-gray-800: #2D2D30;
  --color-gray-900: #1C1C1E;
  
  /* =================================
     语义化颜色
     ================================= */
  /* 背景色 */
  --bg-primary: var(--color-neu-bg);
  --bg-secondary: var(--color-neu-bg-secondary);
  --bg-tertiary: var(--color-neu-card);
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-card: #FFFFFF;
  
  /* 文字色 */
  --text-primary: var(--color-gray-800);
  --text-secondary: var(--color-gray-600);
  --text-tertiary: var(--color-gray-500);
  --text-accent: var(--color-primary);
  
  /* 边框色 */
  --border-color: var(--color-neu-border);
  --border-hover: var(--color-gray-300);
  --glass-border: rgba(159, 41, 159, 0.1);
  
  /* =================================
     毛玻璃效果系统
     ================================= */
  --brand-glass: rgba(159, 41, 159, 0.1);
  --blur-glass: blur(16px);

  /* =================================
     透明度叠加系统
     ================================= */
  /* 背景遮罩 */
  --overlay-backdrop: rgba(0, 0, 0, 0.5);
  
  /* 白色叠加层 */
  --button-overlay-light: rgba(255, 255, 255, 0.15);
  --button-overlay-medium: rgba(255, 255, 255, 0.3);
  --button-overlay-strong: rgba(255, 255, 255, 0.9);
  --text-overlay-light: rgba(255, 255, 255, 0.381);
  
  /* 品牌色叠加层 */
  --brand-overlay-subtle: rgba(159, 41, 159, 0.05);
  --brand-overlay-light: rgba(159, 41, 159, 0.1);
  --brand-overlay-medium: rgba(159, 41, 159, 0.15);
  --brand-overlay-strong: rgba(159, 41, 159, 0.2);
  --brand-overlay-extra: rgba(159, 41, 159, 0.3);
  --brand-overlay-minimal: rgba(159, 41, 159, 0.02);
  
  /* 黑色阴影叠加层 */
  --shadow-light: rgba(0, 0, 0, 0.15);
  --shadow-medium: rgba(0, 0, 0, 0.2);
  --shadow-strong: rgba(0, 0, 0, 0.3);
  --shadow-extra: rgba(0, 0, 0, 0.4);
  --shadow-heavy: rgba(0, 0, 0, 0.5);
  --shadow-dark: rgba(0, 0, 0, 0.6);
  --shadow-darker: rgba(0, 0, 0, 0.7);
  --shadow-darkest: rgba(0, 0, 0, 0.8);
  
  /* 白色边框和按钮叠加层扩展 */
  --button-overlay-soft: rgba(255, 255, 255, 0.148);
  --button-overlay-border: rgba(255, 255, 255, 0.5);
  
  /* 状态色背景叠加层 */
  --warning-bg-overlay: rgba(255, 193, 7, 0.2);
  --success-bg-overlay: rgba(40, 167, 69, 0.2);
  --success-shadow: rgba(40, 167, 69, 0.3);
  
  /* =================================
     阴影系统
     ================================= */
  --shadow-card: 0 10px 25px -5px rgba(159, 41, 159, 0.1);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* =================================
     间距系统 - 紧凑化设计
     ================================= */
  --spacing-1: 0.125rem;  /* 2px */
  --spacing-2: 0.25rem;   /* 4px */
  --spacing-3: 0.375rem;  /* 6px */
  --spacing-4: 0.5rem;    /* 8px */
  --spacing-5: 0.625rem;  /* 10px */
  --spacing-6: 0.75rem;   /* 12px */
  --spacing-8: 1rem;      /* 16px */
  --spacing-10: 1.25rem;  /* 20px */
  --spacing-12: 1.5rem;   /* 24px */
  
  /* =================================
     圆角系统
     ================================= */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  
  /* =================================
     过渡动画
     ================================= */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* =================================
     字体系统
     ================================= */
  --font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* =================================
     响应式断点系统
     ================================= */
  /* 断点定义 - 基于常见设备尺寸 */
  --breakpoint-xl: 1200px;        /* 大屏桌面 */
  --breakpoint-lg: 992px;         /* 桌面 */
  --breakpoint-md: 768px;         /* 平板 */
  --breakpoint-sm: 480px;         /* 手机横屏 */
  --breakpoint-xs: 375px;         /* 手机竖屏 */
  --breakpoint-xxs: 320px;        /* 小尺寸手机 */
  
  /* =================================
     布局系统
     ================================= */
  --header-height: 80px;
  --grid-height: calc(100vh - 200px);
  --grid-columns: 1fr 1fr;
  --grid-rows: 1fr 1fr;

  /* 注意：多订单面板变量已移至主要定义区域，避免重复 */
}

/* =================================
   移动端变量覆盖
   ================================= */
@media (max-width: 768px) { /* var(--breakpoint-md) */
  :root {
    /* 移动端字体大小 - 多订单模块优化 */
    --font-size-mobile-xs: 0.845rem;   /* 13.52px */
    --font-size-mobile-sm: 0.91rem;    /* 14.56px */
    --font-size-mobile-base: 0.975rem; /* 15.6px */
    --font-size-mobile-lg: 1.04rem;    /* 16.64px */
    --font-size-mobile-xl: 1.17rem;    /* 18.72px */
    
    /* 多订单专用字体 */
    --multi-order-font-xs: 0.91rem;    /* 14.56px */
    --multi-order-font-sm: 0.975rem;   /* 15.6px */
    --multi-order-font-base: 1.04rem;  /* 16.64px */
    --multi-order-font-lg: 1.17rem;    /* 18.72px */
    --multi-order-font-xl: 1.3rem;     /* 20.8px */
    
    /* 移动端触摸区域最小尺寸 */
    --touch-target-min: 44px;
    
    /* 移动端间距优化 */
    --mobile-spacing-xs: 0.3rem;      /* 4.8px */
    --mobile-spacing-sm: 0.5rem;      /* 8px */
    --mobile-spacing-md: 0.75rem;     /* 12px */
    --mobile-spacing-lg: 1rem;        /* 16px */
  }
}

/* 超小屏幕极致紧凑设计 */
@media (max-width: 480px) { /* var(--breakpoint-sm) */
  :root {
    /* 超紧凑间距系统 */
    --mobile-ultra-xs: 1px;
    --mobile-ultra-sm: 2px;
    --mobile-ultra-md: 4px;
    --mobile-ultra-lg: 6px;
    --mobile-ultra-xl: 8px;

    /* 极小按钮尺寸 */
    --mobile-btn-height-xs: 20px;
    --mobile-btn-height-sm: 24px;
    --mobile-btn-height-md: 28px;

    /* 紧凑字体 */
    --mobile-compact-xs: 10px;
    --mobile-compact-sm: 11px;
    --mobile-compact-md: 12px;
    --mobile-compact-lg: 13px;
  }
}

/* =================================
   暗色主题 - 紫色Neumorphism协调配色
   ================================= */
[data-theme="dark"] {
  /* 深紫色背景色系 */
  --color-neu-bg-dark: #1a0d1f;        /* 主背景 - 深紫黑 */
  --color-neu-bg-secondary-dark: #2d1b3d;  /* 次背景 - 深紫 */
  --color-neu-card-dark: #3d2a4a;      /* 卡片背景 - 中紫 */
  --color-neu-light-dark: #4a3357;     /* 高光色 - 亮紫 */
  --color-neu-shadow-dark: #0f0612;    /* 阴影色 - 极深紫 */
  --color-neu-border-dark: #5a4067;    /* 边框色 - 紫灰 */

  /* 应用暗色主题配色 */
  --bg-primary: var(--color-neu-bg-dark);
  --bg-secondary: var(--color-neu-bg-secondary-dark);
  --bg-tertiary: var(--color-neu-card-dark);
  --bg-glass: rgba(61, 42, 74, 0.8);
  --bg-card: var(--color-neu-card-dark);

  /* 高对比度文字色 */
  --text-primary: #f0e6f7;    /* 浅紫白 - 主文字 */
  --text-secondary: #d1c4dd;  /* 紫灰 - 次要文字 */
  --text-tertiary: #a888b5;   /* 中紫灰 - 辅助文字 */
  --text-accent: #ff8cff;     /* 亮粉紫 - 强调色 */

  /* 边框色 */
  --border-color: var(--color-neu-border-dark);
  --border-hover: #6b4d78;
  --glass-border: rgba(255, 140, 255, 0.2);

  /* 毛玻璃效果 */
  --brand-glass: rgba(255, 140, 255, 0.15);

  /* 阴影系统 - 暗色主题 */
  --shadow-card: 0 10px 25px -5px rgba(15, 6, 18, 0.4);
  --shadow-sm: 0 1px 2px 0 rgba(15, 6, 18, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(15, 6, 18, 0.4), 0 2px 4px -1px rgba(15, 6, 18, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(15, 6, 18, 0.5), 0 4px 6px -2px rgba(15, 6, 18, 0.4);

  /* 多订单面板专用暗色变量 */
  --overlay-backdrop: rgba(15, 6, 18, 0.8);
  --color-primary-dark: #7A1F7A;
  --color-primary-bg: rgba(255, 140, 255, 0.1);
  --color-primary-bg-light: rgba(255, 140, 255, 0.15);
  --color-primary-selected: rgba(255, 140, 255, 0.3);
  --color-white: #f0e6f7;
  --button-overlay-light: rgba(255, 255, 255, 0.1);
  --button-overlay-medium: rgba(255, 255, 255, 0.2);
  --shadow-extra: rgba(15, 6, 18, 0.6);
  --shadow-medium: rgba(15, 6, 18, 0.4);
}