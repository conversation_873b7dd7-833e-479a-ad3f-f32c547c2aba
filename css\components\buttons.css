/**
 * 按钮组件样式
 * 包含各种按钮类型、状态和尺寸
 */

/* =================================
   基础按钮样式
   ================================= */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  min-height: 36px;
  box-sizing: border-box;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-primary);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* =================================
   按钮变体
   ================================= */
.btn-primary {
  background: var(--color-primary-gradient);
  color: var(--color-white);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-white);
  border-color: var(--color-secondary);
}

.btn-secondary:hover {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover {
  background: var(--color-primary);
  color: var(--color-white);
}

.btn-warning {
  background: var(--color-warning);
  color: var(--color-white);
  border-color: var(--color-warning);
}

.btn-warning:hover {
  background: var(--color-warning-hover);
  border-color: var(--color-warning-hover);
}

.btn-success {
  background: var(--color-success);
  color: var(--color-white);
  border-color: var(--color-success);
}

.btn-success:hover {
  background: var(--color-success-hover);
  border-color: var(--color-success-hover);
}

.btn-error {
  background: var(--color-error);
  color: var(--color-white);
  border-color: var(--color-error);
}

.btn-error:hover {
  background: var(--color-error-hover);
  border-color: var(--color-error-hover);
}

/* =================================
   按钮尺寸
   ================================= */
.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
  min-height: 28px;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
  min-height: 44px;
}

.btn-icon {
  padding: var(--spacing-2);
  min-width: 36px;
  border-radius: 50%;
}

.btn-icon.btn-sm {
  padding: var(--spacing-1);
  min-width: 28px;
}

/* =================================
   按钮状态
   ================================= */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

.btn.success::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-success);
  font-weight: bold;
}

/* =================================
   按钮组
   ================================= */
.btn-group {
  display: flex;
  gap: var(--spacing-2);
}

.btn-group .btn {
  flex: 1;
}

.btn-group.btn-group-vertical {
  flex-direction: column;
}

/* =================================
   特殊按钮样式
   ================================= */
.btn-glass {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-glass);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
}

.btn-glass:hover {
  background: var(--brand-glass);
  border-color: var(--color-primary);
}

/* =================================
   移动端优化
   ================================= */
@media (max-width: 768px) {
  .btn {
    min-height: var(--touch-target-min);
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-mobile-sm);
  }
  
  .btn-sm {
    min-height: 36px;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-mobile-xs);
  }
  
  .btn-icon {
    min-width: var(--touch-target-min);
    min-height: var(--touch-target-min);
  }
  
  .btn-group {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .btn-group.btn-group-horizontal {
    flex-direction: row;
    gap: var(--spacing-2);
  }
}

@media (max-width: 480px) {
  .btn {
    font-size: var(--mobile-compact-sm);
    min-height: var(--mobile-btn-height-md);
  }
  
  .btn-sm {
    font-size: var(--mobile-compact-xs);
    min-height: var(--mobile-btn-height-sm);
    padding: var(--mobile-ultra-sm) var(--mobile-ultra-md);
  }
}

/* =================================
   动画
   ================================= */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}