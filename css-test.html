<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS重构测试页面</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
        }
        .test-section h2 {
            color: var(--color-primary);
            margin-bottom: 1rem;
        }
        .component-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部测试 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🧪</span>
                    <span>CSS重构测试</span>
                </h1>
                <div class="header-controls">
                    <div class="theme-toggle">
                        <select class="language-select">
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                        </select>
                        <button type="button" class="btn btn-icon">🌙</button>
                    </div>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="workspace">
                <!-- 按钮组件测试 -->
                <section class="test-section">
                    <h2>🔘 按钮组件测试</h2>
                    <div class="component-demo">
                        <button class="btn btn-primary">主要按钮</button>
                        <button class="btn btn-secondary">次要按钮</button>
                        <button class="btn btn-outline">边框按钮</button>
                        <button class="btn btn-warning">警告按钮</button>
                        <button class="btn btn-success">成功按钮</button>
                        <button class="btn btn-error">错误按钮</button>
                    </div>
                    <div class="component-demo">
                        <button class="btn btn-primary btn-sm">小按钮</button>
                        <button class="btn btn-primary">标准按钮</button>
                        <button class="btn btn-primary btn-lg">大按钮</button>
                        <button class="btn btn-icon">⚙️</button>
                    </div>
                </section>

                <!-- 表单组件测试 -->
                <section class="test-section">
                    <h2>📝 表单组件测试</h2>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                            <label>文本输入</label>
                            <input type="text" placeholder="请输入文本">
                        </div>
                        <div class="form-group">
                            <label>选择器</label>
                            <select>
                                <option>选项1</option>
                                <option>选项2</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>文本域</label>
                            <textarea placeholder="请输入多行文本"></textarea>
                        </div>
                        <div class="form-group">
                            <label>复选框</label>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span>选项A</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox">
                                    <span>选项B</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 卡片组件测试 -->
                <section class="test-section">
                    <h2>🃏 卡片组件测试</h2>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="compact-card">
                            <div class="section-header">
                                <h3>普通卡片</h3>
                            </div>
                            <div class="panel-content">
                                <p>这是一个普通的卡片内容</p>
                            </div>
                        </div>
                        <div class="status-card success">
                            <h4>成功状态</h4>
                            <p>这是成功状态的卡片</p>
                        </div>
                        <div class="status-card warning">
                            <h4>警告状态</h4>
                            <p>这是警告状态的卡片</p>
                        </div>
                    </div>
                </section>

                <!-- 网格布局测试 -->
                <section class="test-section">
                    <h2>📐 网格布局测试</h2>
                    <div class="three-column-layout" style="height: 200px;">
                        <div class="column-left" style="background: rgba(255,0,0,0.1);">
                            <div class="compact-card">
                                <h4>左列</h4>
                                <p>左列内容</p>
                            </div>
                        </div>
                        <div class="column-middle" style="background: rgba(0,255,0,0.1);">
                            <div class="compact-card">
                                <h4>中列</h4>
                                <p>中列内容</p>
                            </div>
                        </div>
                        <div class="column-right" style="background: rgba(0,0,255,0.1);">
                            <div class="compact-card">
                                <h4>右列</h4>
                                <p>右列内容</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 工具类测试 -->
                <section class="test-section">
                    <h2>🔧 工具类测试</h2>
                    <div class="flex items-center justify-between p-4 bg-secondary rounded-lg mb-4">
                        <span class="text-primary font-semibold">Flex布局测试</span>
                        <span class="text-sm text-secondary">工具类组合</span>
                    </div>
                    <div class="grid grid-cols-4 gap-2">
                        <div class="p-3 bg-tertiary rounded text-center text-xs">间距测试</div>
                        <div class="p-3 bg-tertiary rounded text-center text-sm">字体大小</div>
                        <div class="p-3 bg-tertiary rounded text-center text-base">测试</div>
                        <div class="p-3 bg-tertiary rounded text-center text-lg">工具类</div>
                    </div>
                </section>
            </div>
        </main>

        <!-- 状态栏测试 -->
        <footer class="status-bar">
            <div class="status-info">
                <span class="status-item connected">🔌 已连接</span>
                <span class="status-item">📊 数据已加载</span>
                <span class="status-item">⏰ 12:34</span>
            </div>
        </footer>
    </div>

    <script>
        // 简单的交互测试
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    console.log('按钮点击测试:', this.textContent);
                });
            });
            
            console.log('CSS重构测试页面加载完成');
            console.log('CSS变量测试:', getComputedStyle(document.documentElement).getPropertyValue('--color-primary'));
        });
    </script>
</body>
</html>