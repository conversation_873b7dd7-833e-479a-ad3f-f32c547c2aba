<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局调试</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .debug-info {
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid red;
            padding: 10px;
            margin: 5px;
        }
        .column-left { background: rgba(255, 0, 0, 0.3) !important; }
        .column-middle { background: rgba(0, 255, 0, 0.3) !important; }
        .column-right { background: rgba(0, 0, 255, 0.3) !important; }
    </style>
</head>
<body>
    <div id="app">
        <div id="workspace" class="workspace" style="display: block;">
            <form id="orderForm" class="three-column-layout">
                <div class="column-left column-mobile">
                    <div class="debug-info">
                        <h3>左列 - 应该可见</h3>
                        <p>如果你能看到这个红色区域，说明左列是可见的</p>
                    </div>
                </div>
                
                <div class="column-middle column-mobile">
                    <div class="debug-info">
                        <h3>中列 - 应该可见</h3>
                        <p>如果你能看到这个绿色区域，说明中列是可见的</p>
                    </div>
                </div>
                
                <div class="column-right column-mobile">
                    <div class="debug-info">
                        <h3>右列 - 应该可见</h3>
                        <p>如果你能看到这个蓝色区域，说明右列是可见的</p>
                    </div>
                </div>
            </form>
        </div>
    </div>
</body>
</html>