/* 多订单卡片样式 - 直立卡片设计，粉红主题配色，移动端优化 */

/* 
 * 注意：CSS变量已在 base/variables.css 中统一定义
 * 这里使用现有变量：var(--color-primary) 等
 */

/* 多订单面板基础样式 */
.multi-order-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--overlay-backdrop);
    z-index: 1000;
    overflow-y: auto;
    padding: 16px;
    box-sizing: border-box;
    display: none; /* 默认隐藏 */
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.multi-order-panel:not(.hidden) {
    display: flex !important;
}

.multi-order-content {
    max-width: 1200px;
    width: 90vw;
    margin: 20px auto;
    background: var(--bg-tertiary);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}

/* 多订单组件专用样式 - 使用全局变量支持暗色模式 */
.multi-order-panel,
.multi-order-content,
.order-card {
  /* 组件使用全局变量，无需重复定义 */
}

/* 多订单头部样式 - 使用新色彩系统 */
.multi-order-header {
    background: var(--brand-gradient);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 50px; /* 按设计文档要求 */
}

.multi-order-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

/* 头部左侧区域 */
.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.btn-header-back {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-header-back:hover {
    background: var(--button-overlay-medium);
    transform: translateX(-2px);
}

/* 订单统计样式已移除 */

.btn-header-action {
    background: var(--button-overlay-strong);
    border: 1px solid var(--button-overlay-strong);
    color: var(--color-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-header-action:hover {
    background: var(--color-white);
    transform: translateY(-1px);
}

/* 批量设置头部容器 */
.batch-settings-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
}

/* 筛选排序控制栏 - 按设计文档要求40px高度 */
.filter-sort-controls {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.control-label {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-accent);
    white-space: nowrap;
}

.filter-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-sm);
    min-width: 80px;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--brand-glass);
}

.view-toggle {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-sm);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.view-toggle.active {
    background: var(--color-primary);
    color: var(--color-white);
}

.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.quick-btn {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.quick-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
    transform: translateY(-1px);
}

.multi-order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.order-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.order-count {
    font-weight: 600;
    font-size: 1rem;
}

/* date-range 样式已移除 */

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-actions .btn {
    padding: 6px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
}

/* 批量控制面板 - 紧凑设计 */
.batch-control-panel {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--text-overlay-light);
    padding: 12px 16px;
}

/* 新的内联批量控制面板 */
.batch-control-panel-inline {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--color-white);
    padding: 4px 8px;
    border-radius: 0;
    margin-bottom: 0;
}

/* 简化的批量控制面板 - 只包含设置选项 */
.batch-control-panel-simple {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--color-white);
    padding: 6px 12px;
    border-radius: 0;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 批量设置主要布局 - 水平一排显示 */
.batch-inline-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    min-height: 40px;
}

.batch-settings-inline {
    display: flex;
    align-items: center;
    gap: 8px;
}

.batch-summary-inline {
    font-size: 13px;
    font-weight: 600;
    color: var(--color-white);
}

.batch-actions-inline {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 在头部容器中的批量控制面板样式 */
.batch-settings-header .batch-control-panel-inline {
    background: var(--brand-overlay-subtle);
    color: var(--color-primary);
    border-radius: 0;
    border-bottom: 1px solid var(--brand-overlay-light);
}

/* 简化批量控制面板在头部容器中的样式 */
.batch-settings-header .batch-control-panel-simple {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.batch-settings-header .batch-select-compact,
.batch-settings-header .batch-apply-compact {
    background: var(--brand-overlay-light);
    border: 1px solid var(--brand-overlay-strong);
    color: var(--color-primary);
}

/* 简化批量面板中的控件样式 */
.batch-settings-header .batch-control-panel-simple .batch-select-compact,
.batch-settings-header .batch-control-panel-simple .batch-apply-compact {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.batch-settings-header .batch-apply-compact:hover {
    background: var(--brand-overlay-medium);
}

.batch-settings-header .batch-btn-compact {
    background: var(--brand-overlay-light);
    border: 1px solid var(--brand-overlay-strong);
    color: var(--color-primary);
}

.batch-settings-header .batch-btn-compact:hover {
    background: var(--brand-overlay-medium);
}

.batch-inline-header {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.batch-settings-inline {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 200px;
}

/* 批量标题样式已移除 */

.batch-controls-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
}

/* 简化面板中的控制行样式 */
.batch-control-panel-simple .batch-controls-row {
    justify-content: center;
    gap: 12px;
}

.batch-control-item {
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.batch-select-compact {
    padding: 2px 6px;
    border: 1px solid var(--button-overlay-medium);
    border-radius: 4px;
    background: var(--button-overlay-light);
    color: var(--color-white);
    font-size: 11px;
    min-width: 90px;
    max-width: 110px;
    outline: none;
    cursor: pointer;
}

.batch-apply-compact {
    padding: 2px 8px;
    background: var(--button-overlay-medium);
    border: 1px solid var(--button-overlay-medium);
    border-radius: 4px;
    color: var(--color-white);
    font-size: 11px;
    cursor: pointer;
    transition: background var(--transition-fast);
    min-width: 40px;
    outline: none;
}

.batch-apply-compact:hover {
    background: var(--button-overlay-medium);
}

.batch-summary-inline {
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

.batch-actions-inline {
    display: flex;
    gap: 6px;
    align-items: center;
}

.batch-btn-compact {
    padding: 4px 8px;
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    border-radius: 4px;
    color: var(--color-white);
    font-size: 11px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 2px;
    outline: none;
}

.batch-btn-compact:hover {
    background: var(--button-overlay-medium);
    transform: translateY(-1px);
}

.batch-btn-compact .btn-icon {
    font-size: 14px;
}

.batch-btn-compact .btn-text {
    font-size: 12px;
}

/* 响应式布局优化 - 保持水平排列但优化间距 */
@media (max-width: 768px) {
    .batch-inline-header {
        gap: 8px;
        min-height: 36px;
    }
    
    .batch-settings-inline {
        gap: 8px;
    }
    
    .batch-controls-row {
        gap: 6px;
        flex-wrap: wrap;
    }
    
    .batch-control-item {
        gap: 3px;
    }
    
    .batch-select-compact {
        min-width: 80px;
        max-width: 100px;
        font-size: 10px;
    }
    
    .batch-apply-compact {
        font-size: 10px;
        padding: 2px 6px;
    }
    
    .batch-actions-inline {
        gap: 4px;
    }
    
    .batch-btn-compact .btn-text {
        display: none;
    }
    
    .batch-btn-compact {
        padding: 3px 6px;
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .batch-control-panel-inline {
        padding: 2px 4px;
    }
    
    .batch-inline-header {
        gap: 6px;
        min-height: 32px;
    }
    
    /* 批量标题样式已移除 */
    
    .batch-controls-row {
        gap: 4px;
    }
    
    .batch-select-compact {
        min-width: 70px;
        max-width: 90px;
        font-size: 9px;
        padding: 1px 4px;
    }
    
    .batch-apply-compact {
        font-size: 9px;
        padding: 1px 6px;
        min-width: 30px;
    }
    
    .batch-btn-compact {
        padding: 2px 4px;
        font-size: 9px;
    }
    
    .batch-summary-inline {
        font-size: 11px;
        text-align: center;
    }
    
    /* 头部批量设置响应式 - 简化版本 */
    .batch-settings-header .batch-control-panel-simple {
        padding: 3px 6px;
    }
    
    .batch-settings-header .batch-controls-row {
        gap: 3px;
    }
}

.batch-control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.batch-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 16px;
}

.batch-icon {
    font-size: 18px;
}

/* 下拉菜单设计 */
.batch-dropdown {
    position: relative;
}

.batch-dropdown-btn {
    background: var(--button-overlay-medium);
    border: 1px solid var(--button-overlay-medium);
    color: var(--button-overlay-soft);
    padding: 6px 12px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all var(--transition-fast) ease;
}

.batch-dropdown-btn:hover {
    background: var(--color-primary-hover);
    border-color: var(--button-overlay-border);
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform var(--transition-fast) ease;
}

.batch-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-tertiary);
    border-radius: 12px;
    box-shadow: 0 8px 24px var(--shadow-light);
    padding: 16px;
    min-width: 280px;
    z-index: 1001;
    display: none;
    color: var(--text-primary);
}

.batch-option {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.batch-option:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.batch-option-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 14px;
}

.batch-option-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 8px;
}

.batch-apply-btn {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--color-white);
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
}

.batch-apply-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-medium), 0 0 12px var(--color-primary-selected);
}

/* 批量操作按钮区域 */
.batch-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding-top: 16px;
    border-top: 2px solid var(--border-color);
}

/* 批量操作按钮 - 减小30%尺寸，保持主界面设计风格 */
.batch-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px; /* 从6px减少到4px */
    padding: 6px 8px; /* 从8px 12px减少到6px 8px */
    border: 1px solid transparent; /* 与主界面按钮保持一致 */
    border-radius: 6px; /* 从8px减少到6px */
    font-size: 10px; /* 从12px减少到10px */
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 28px; /* 新增最小高度控制 */
    user-select: none; /* 与主界面按钮保持一致 */
    box-sizing: border-box; /* 与主界面按钮保持一致 */
}

.btn-create-all {
    background: var(--color-success-gradient);
    color: var(--color-white);
    grid-column: 1 / -1; /* 跨两列 */
}

.btn-select-all {
    background: var(--color-info-gradient);
    color: var(--color-white);
}

.btn-clear-all {
    background: var(--color-error-gradient);
    color: var(--color-white);
}

.batch-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-medium);
}

.batch-summary {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

/* 订单卡片网格 */
.multi-order-list {
    padding: 4px 6px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 4px;
    background: var(--bg-primary);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    min-height: 300px;
}

/* 多订单底部样式 - 重新设计为两层布局 */
.multi-order-footer {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    border-top: 1px solid var(--button-overlay-medium);
    border-radius: 0 0 16px 16px;
    position: sticky;
    bottom: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
}

/* 批量设置控制面板 - 底部样式 */
.batch-settings-footer {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: var(--spacing-2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.footer-actions-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-2);
    flex-wrap: wrap;
}

/* 底部操作按钮分组 */
.footer-actions-left,
.footer-actions-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.footer-actions-center {
    flex: 1;
    text-align: center;
}

/* Footer按钮样式 - 与导航栏颜色同步 */
.btn-footer {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: 2px 6px; /* 减少30%：原本更大的padding */
    font-size: 0.85rem; /* 减少15%：1rem → 0.85rem */
    border-radius: 4px;
    transition: all var(--transition-fast);
    min-height: auto;
}

.btn-footer:hover {
    background: var(--button-overlay-medium);
    transform: translateY(-1px);
}

.btn-footer-primary {
    background: var(--button-overlay-strong);
    border: 1px solid var(--button-overlay-strong);
    color: var(--color-primary);
    padding: 2px 8px; /* 略大一点作为主要按钮 */
    font-size: 0.85rem; /* 减少15% */
    font-weight: 600;
    border-radius: 4px;
    transition: all var(--transition-fast);
    min-height: auto;
}

.btn-footer-primary:hover {
    background: var(--color-white);
    transform: translateY(-1px) scale(1.02);
}

.footer-count {
    color: var(--color-white);
    font-size: 0.85rem; /* 减少15% */
    font-weight: 500;
    white-space: nowrap;
    padding: 0 4px;
}

/* Footer响应式设计 */
@media (max-width: 768px) {
    .footer-actions-row {
        justify-content: center;
        gap: 2px;
    }
    
    .btn-footer,
    .btn-footer-primary {
        padding: 1px 4px;
        font-size: 0.8rem; /* 进一步减小 */
    }
    
    .footer-count {
        font-size: 0.8rem;
        order: -1; /* 将计数移到前面 */
        flex: 1;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .multi-order-footer {
        padding: 2px 4px;
        min-height: 20px;
    }
    
    .footer-actions-row {
        gap: 1px;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    
    .btn-footer,
    .btn-footer-primary {
        padding: 1px 3px;
        font-size: 0.75rem;
        min-width: auto;
    }
    
    .footer-count {
        font-size: 0.75rem;
        width: 100%;
        text-align: center;
        margin-bottom: 2px;
    }
}


.batch-create-status {
    background: var(--bg-secondary);
    padding: 12px 20px;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    text-align: center;
}

/* 紧凑卡片设计 - 按设计文档要求 */
.order-card {
    background: var(--bg-tertiary);
    border-radius: 12px;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal) ease;
    overflow: hidden;
    border: 1px solid var(--border-color);
    position: relative;
    min-height: 80px; /* 按设计文档紧凑要求 */
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px -5px var(--brand-overlay-strong);
    border-color: var(--color-primary);
}

.order-card.selected {
    border-color: var(--color-primary);
    background: linear-gradient(135deg, var(--brand-glass) 0%, var(--brand-overlay-subtle) 100%);
}

/* 紧凑同行布局样式 */
.compact-inline-layout {
    --item-height: 24px;
    --item-spacing: 4px;
    --font-size: var(--font-sm);
    --line-height: var(--line-height-tight);
}

.inline-item {
    display: inline-flex;
    align-items: center;
    height: var(--item-height);
    margin-right: var(--item-spacing);
    font-size: var(--font-size);
    line-height: var(--line-height);
    white-space: nowrap;
}

.inline-label {
    font-weight: 600;
    margin-right: var(--spacing-xs);
    color: var(--color-primary);
}

.inline-value {
    color: var(--text-secondary);
}

/* 状态图标样式 */
.status-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: var(--spacing-xs);
    vertical-align: middle;
}

.status-ready { color: var(--color-success-green); }
.status-warning { color: var(--color-warning-amber); }
.status-error { color: var(--color-error-red); }
.status-processing { color: var(--color-info-blue); }
.status-completed { color: var(--color-success-green); }
.status-cancelled { color: var(--color-error-red); }
.status-complete { color: var(--color-success-green); }
.status-progress { color: var(--color-warning-amber); }
.status-cancel { color: var(--color-error-red); }
.status-normal { color: var(--color-success-green); }

.order-card.paging-order {
    border-left: 4px solid var(--color-warning-yellow);
}

/* 卡片头部 */
.order-card-header {
    padding: 1px 4px;
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--color-white);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 15px;
}

.order-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--color-white);
}

.order-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-number {
    font-weight: 600;
    font-size: 14px;
}

.paging-badge {
    background: var(--warning-bg-overlay);
    color: var(--color-warning-yellow);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
}

.order-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}

.status-ready {
    background: var(--success-bg-overlay);
    color: var(--color-success-green);
}

/* 卡片主体 */
.order-card-body {
    padding: 1px 4px;
    cursor: pointer;
}

.order-card-body:hover {
    background: var(--color-primary-bg);
}

/* 卡片底部操作区 */
.order-card-footer {
    padding: 1px 4px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    min-height: 32px;
}

/* 订单卡片操作按钮 - 减小30%尺寸 */
.btn-card-action {
    display: flex;
    align-items: center;
    gap: 4px; /* 从6px减少到4px */
    padding: 6px 11px; /* 从8px 16px减少到6px 11px */
    border: none;
    border-radius: 6px; /* 从8px减少到6px */
    font-size: 11px; /* 从13px减少到11px */
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    min-height: 25px; /* 从36px减少到25px */
}

.btn-create {
    background: var(--color-success-gradient);
    color: var(--color-white);
    width: 100%;
}

.btn-create:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--success-shadow);
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 600;
}

/* 移动端优化 - 方案一：紧凑高效型 */

/* 平板端优化 (768px-481px) */
@media (max-width: 768px) and (min-width: 481px) {
    .multi-order-panel {
        padding: 12px;
        justify-content: flex-start;
        padding-top: 40px;
    }

    /* 移动端底部操作栏优化 */
    .footer-actions-row {
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .footer-actions-left,
    .footer-actions-right {
        width: 100%;
        justify-content: center;
    }

    .footer-actions-center {
        order: -1; /* 将计数器移到顶部 */
    }
    
    .multi-order-content {
        width: 95vw;
        margin: 0 auto;
    }

    .batch-control-panel {
        padding: 10px 16px;
        background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
        color: var(--color-white);
        text-shadow: 0 1px 2px var(--shadow-strong);
    }

    .batch-title {
        font-size: 15px;
        font-weight: 600;
    }

    .batch-dropdown-btn {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 44px;
        background: var(--button-overlay-light);
        border: 1px solid var(--button-overlay-medium);
    }

    .batch-dropdown-content {
        min-width: 280px;
        padding: 16px;
    }

    .multi-order-list {
        grid-template-columns: 1fr 1fr;
        gap: 4px;
        padding: 4px;
    }

    .order-card-header {
        padding: 1px 3px;
        background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
        color: var(--color-white);
        text-shadow: 0 1px 2px var(--shadow-strong);
    }

    .order-card-body {
        padding: 1px 3px;
    }

    .order-card-footer {
        padding: 1px 3px;
    }

    .btn-card-action {
        padding: 10px 16px;
        font-size: 13px;
        min-height: 44px;
        font-weight: 600;
    }

    .batch-actions {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .btn-create-all {
        grid-column: 1 / -1;
        min-height: 31px; /* 从44px减少30%到31px */
        font-size: 9px; /* 进一步减小移动端字体 */
    }
}

/* 大屏手机端优化 (480px-376px) - 智能双列布局 */
@media (max-width: 480px) and (min-width: 376px) {
    .multi-order-panel {
        padding: 8px;
        justify-content: flex-start;
        padding-top: 30px;
    }
    
    .multi-order-content {
        width: 96vw;
        margin: 0 auto;
    }

    .batch-control-panel {
        padding: 8px 12px;
        background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
        color: var(--color-white);
        text-shadow: 0 1px 2px var(--shadow-extra);
    }

    .batch-title {
        font-size: 14px;
        font-weight: 600;
    }

    .batch-dropdown-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-height: 44px;
        background: var(--button-overlay-light);
        border: 1px solid var(--button-overlay-medium);
    }

    .batch-dropdown-content {
        min-width: 260px;
        padding: 12px;
    }

    .multi-order-list {
        grid-template-columns: 1fr 1fr;
        gap: 3px;
        padding: 3px;
    }

    .order-card {
        font-size: 13px;
    }

    .order-card-header {
        padding: 1px 2px;
        background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
        color: var(--color-white);
        text-shadow: 0 1px 2px var(--shadow-extra);
    }

    .order-checkbox {
        width: 20px;
        height: 20px;
        accent-color: var(--color-white);
    }

    .order-card-body {
        padding: 1px 2px;
    }

    .order-card-footer {
        padding: 1px 2px;
    }

    .btn-card-action {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 44px;
        font-weight: 600;
    }

    .batch-actions {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .batch-action-btn {
        min-height: 44px;
        font-size: 13px;
        font-weight: 600;
    }

    .btn-create-all {
        grid-column: 1;
    }
}

/* 小屏手机端优化 (375px以下) - 单列紧凑布局 */
@media (max-width: 375px) {
    .multi-order-panel {
        padding: 6px;
        justify-content: flex-start;
        padding-top: 20px;
    }
    
    .multi-order-content {
        width: 98vw;
        margin: 0 auto;
    }

    .batch-control-panel {
        padding: 8px 10px;
        background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
        color: var(--color-white);
        text-shadow: 0 1px 3px var(--shadow-heavy);
    }

    .batch-title {
        font-size: 13px;
        font-weight: 600;
    }

    .batch-dropdown-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-height: 44px;
        background: var(--button-overlay-light);
        border: 1px solid var(--button-overlay-medium);
        width: 100%;
        justify-content: space-between;
    }

    .batch-dropdown-content {
        min-width: calc(100vw - 32px);
        left: 0;
        right: 0;
        margin: 0 16px;
        padding: 12px;
    }

    .multi-order-list {
        grid-template-columns: 1fr;
        gap: 4px;
        padding: 3px;
    }

    .order-card {
        font-size: 13px;
    }

    .order-card-header {
        padding: 1px 2px;
        background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
        color: var(--color-white);
        text-shadow: 0 1px 3px var(--shadow-heavy);
    }

    .order-number {
        font-size: 13px;
        font-weight: 600;
    }

    .order-checkbox {
        width: 22px;
        height: 22px;
        accent-color: var(--color-white);
    }

    .order-card-body {
        padding: 1px 2px;
    }

    .order-card-footer {
        padding: 1px 2px;
    }

    .btn-card-action {
        padding: 12px 16px;
        font-size: 13px;
        min-height: 48px;
        font-weight: 600;
        border-radius: 8px;
    }

    .batch-actions {
        grid-template-columns: 1fr;
        gap: 8px;
        padding-top: 12px;
    }

    .batch-action-btn {
        min-height: 48px;
        font-size: 14px;
        font-weight: 600;
        padding: 12px 16px;
    }

    .btn-create-all {
        grid-column: 1;
    }

    /* 优化批量操作按钮间距 */
    .batch-option {
        margin-bottom: 12px;
        padding-bottom: 12px;
    }

    .batch-option-select {
        min-height: 44px;
        font-size: 14px;
    }

    .batch-apply-btn {
        min-height: 44px;
        font-size: 13px;
        padding: 10px 16px;
    }
}

/* 极小屏幕优化 (320px以下) */
@media (max-width: 320px) {
    .multi-order-panel {
        padding: 4px;
    }

    .batch-control-panel {
        padding: 6px 8px;
    }

    .batch-title {
        font-size: 12px;
    }

    .batch-dropdown-btn {
        padding: 6px 10px;
        font-size: 11px;
        min-height: 44px;
    }

    .order-card-header {
        padding: 1px;
    }

    .order-card-body {
        padding: 1px;
    }

    .order-card-footer {
        padding: 1px;
    }

    .btn-card-action {
        padding: 10px 12px;
        font-size: 12px;
        min-height: 44px;
    }

    .batch-action-btn {
        min-height: 44px;
        font-size: 12px;
        padding: 10px 12px;
    }
}

/* 移动端专用增强样式 */
@media (max-width: 768px) {
    /* 高对比度文字增强 */
    .batch-control-panel {
        color: white !important;
        text-shadow: 0 1px 3px var(--shadow-dark);
    }

    .order-card-header {
        color: white !important;
        text-shadow: 0 1px 3px var(--shadow-dark);
    }

    /* 选中状态增强 */
    .order-card.selected {
        border-color: var(--color-primary-dark);
        border-width: 3px;
        background: linear-gradient(135deg, var(--color-primary-bg) 0%, var(--color-primary-bg-light) 100%);
        box-shadow: 0 2px 12px var(--color-primary-selected);
    }

    /* 触摸反馈增强 */
    .btn-card-action:active,
    .batch-action-btn:active,
    .batch-apply-btn:active {
        transform: translateY(1px) scale(0.98);
        transition: all var(--transition-fast) ease;
    }

    /* 复选框增强 */
    .order-checkbox {
        cursor: pointer;
        transform: scale(1.1);
    }

    .order-checkbox:checked {
        accent-color: var(--color-primary);
    }

    /* 下拉菜单移动端优化 */
    .batch-dropdown-content {
        border-radius: 12px;
        box-shadow: 0 8px 32px var(--shadow-medium);
            backdrop-filter: var(--blur-glass);
        background: var(--bg-tertiary);
    }

    /* 按钮间距优化 */
    .batch-actions {
        margin-top: 8px;
    }

    .batch-action-btn + .batch-action-btn {
        margin-top: 0;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .multi-order-panel {
        padding: 8px 16px;
    }

    .batch-control-panel {
        padding: 6px 16px;
    }

    .multi-order-list {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
    }

    .order-card-header {
        padding: 1px 3px;
    }

    .order-card-body {
        padding: 1px 3px;
    }

    .order-card-footer {
        padding: 1px 3px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .batch-control-panel,
    .order-card-header {
        background: var(--color-primary-dark) !important;
        color: white !important;
        text-shadow: none;
    }

    .order-card.selected {
        border-color: var(--color-primary-dark);
        border-width: 4px;
    }

    .btn-card-action {
        border: 2px solid var(--color-primary-dark);
    }
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideIn 0.3s ease-out;
}

.batch-dropdown-content {
    animation: slideIn 0.2s ease-out;
}

/* 关闭按钮样式 */
.multi-order-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--overlay-backdrop);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    z-index: 1002;
    transition: all var(--transition-fast) ease;
}

.multi-order-close-btn:hover {
    background: var(--shadow-darker);
}

/* 移动端关闭按钮优化 */
@media (max-width: 768px) {
    .multi-order-close-btn {
        top: 12px;
        right: 12px;
        width: 44px;
        height: 44px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.6);
        -webkit-backdrop-filter: var(--blur-glass);
        backdrop-filter: var(--blur-glass);
        border: 2px solid var(--button-overlay-medium);
    }

    .multi-order-close-btn:active {
        transform: scale(0.95);
        background: var(--shadow-darkest);
    }
}

@media (max-width: 375px) {
    .multi-order-close-btn {
        top: 8px;
        right: 8px;
        width: 48px;
        height: 48px;
        font-size: 22px;
    }
}

/* 紧凑按钮样式 */
.btn-compact {
    padding: 2px 6px;
    font-size: var(--font-sm);
    height: 20px;
    min-width: 40px;
    border-radius: 4px;
    border: 1px solid var(--color-primary);
    background: var(--brand-gradient);
    color: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    margin-right: 2px;
}

.btn-compact:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px var(--brand-overlay-extra);
}

/* 三列移动端布局 */
.three-column-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 4px;
    height: calc(100vh - 70px);
    overflow: hidden;
    padding: 4px;
}

.column-mobile {
    overflow-y: auto;
    padding: 4px;
    border-radius: 8px;
    background: var(--brand-overlay-minimal);
}

/* 滚动条样式已在 base/reset.css 中统一定义 */

/* 响应式断点增强 */
@media (max-width: 1200px) {
    .compact-card { min-height: 75px; }
    .inline-item { font-size: 10px; }
}

@media (max-width: 992px) {
    .three-column-mobile { gap: 3px; }
    .column-mobile { padding: 3px; }
}

@media (max-width: 768px) {
    .compact-card {
        min-height: 70px;
        padding: 4px 6px;
    }
    .inline-item {
        font-size: 9px;
        --item-height: 20px;
    }
}

@media (max-width: 480px) {
    .three-column-mobile {
        gap: 2px;
        padding: 2px;
    }
    .compact-card {
        min-height: 65px;
        padding: 3px 5px;
    }
}

@media (max-width: 375px) {
    .inline-item {
        font-size: 8px;
        --item-height: 18px;
    }
    .compact-card { min-height: 60px; }
}