/**
 * 卡片组件样式
 * 包含面板、紧凑卡片、毛玻璃卡片等
 */

/* =================================
   基础面板样式
   ================================= */
.panel {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.panel:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-card);
}

/* =================================
   紧凑毛玻璃卡片
   ================================= */
.compact-card {
  padding: 8px 10px;
  margin-bottom: 6px;
  border-radius: 12px;
  background: var(--bg-glass);
  backdrop-filter: var(--blur-glass);
  -webkit-backdrop-filter: var(--blur-glass);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--glass-border);
  min-height: auto;
  height: auto;
  flex-shrink: 0;
  transition: all var(--transition-normal);
}

.compact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px -5px rgba(159, 41, 159, 0.2);
  border-color: var(--color-primary);
}

/* =================================
   面板头部
   ================================= */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.section-header h3 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.section-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* =================================
   面板内容 - 优化布局和分组
   ================================= */
.panel-content {
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2); /* 统一内容间距 */
}

/* 紧凑内联布局 - 优化表单字段排列 */
.compact-inline-layout {
  --item-height: 20px;
  --item-spacing: 4px;
  --font-size: 11px;
  --line-height: 1.1;

  /* 表单字段网格布局 */
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列布局 */
  gap: var(--spacing-3) var(--spacing-4); /* 行间距 列间距 */
  align-items: start;
}

/* 单列字段（如文本域）跨越两列 */
.compact-inline-layout .form-group.full-width {
  grid-column: 1 / -1;
}

/* 重要字段突出显示 */
.compact-inline-layout .form-group.primary {
  grid-column: 1 / -1;
  background: var(--color-primary-bg);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-primary-bg-light);
}

.inline-item {
  display: inline-flex;
  align-items: center;
  height: var(--item-height);
  margin-right: var(--item-spacing);
  font-size: var(--font-size);
  line-height: var(--line-height);
  white-space: nowrap;
}

.inline-label {
  font-weight: 600;
  margin-right: 2px;
  color: var(--color-primary);
}

.inline-value {
  color: var(--text-secondary);
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-width: 200px;
}

/* =================================
   状态卡片
   ================================= */
.status-card {
  background: var(--bg-glass);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  backdrop-filter: var(--blur-glass);
}

.status-card.success {
  border-color: var(--color-success);
  background: var(--color-success-light);
}

.status-card.warning {
  border-color: var(--color-warning);
  background: var(--color-warning-light);
}

.status-card.error {
  border-color: var(--color-error);
  background: var(--color-error-light);
}

/* =================================
   AI状态卡片
   ================================= */
.ai-status {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.ai-status .inline-item {
  background: rgba(159, 41, 159, 0.05);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--glass-border);
}

/* =================================
   预览内容
   ================================= */
.preview-content {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

/* =================================
   图片预览容器
   ================================= */
.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
}

.image-preview {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: var(--radius-md);
  overflow: hidden;
  border: 2px solid var(--border-color);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-preview .remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: var(--color-error);
  color: var(--color-white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* =================================
   上传状态
   ================================= */
.upload-status {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  text-align: center;
}

.upload-status.success {
  background: var(--color-success-light);
  color: var(--color-success);
}

.upload-status.error {
  background: var(--color-error-light);
  color: var(--color-error);
}

.upload-status.processing {
  background: var(--color-info-light);
  color: var(--color-info);
}

/* =================================
   移动端优化
   ================================= */
@media (max-width: 768px) {
  .compact-card {
    padding: 7px 9px;
    margin-bottom: 4px;
  }
  
  .section-header {
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .section-header h3 {
    font-size: var(--font-size-mobile-base);
  }
  
  .panel-content {
    padding: var(--spacing-3);
    gap: var(--spacing-2);
  }

  /* 移动端单列布局 */
  .compact-inline-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .inline-item {
    font-size: 10px;
    height: 16px;
  }
  
  .image-preview {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .compact-card {
    padding: 6px 8px;
    margin-bottom: 3px;
  }
  
  .section-header {
    padding: var(--mobile-ultra-md);
    flex-direction: column;
    gap: var(--mobile-ultra-sm);
    align-items: flex-start;
  }
  
  .section-header h3 {
    font-size: var(--mobile-compact-md);
  }
  
  .panel-content {
    padding: var(--mobile-ultra-md);
  }
  
  .inline-item {
    font-size: 9px;
    padding: 1px 3px;
  }
}