<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言下拉框检查器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .inspector {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: 500;
        }
        .status.success { background: #e8f5e8; color: #2d5a2d; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .dropdown-demo {
            border: 2px dashed #ccc;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .log-entry {
            padding: 8px;
            border-left: 3px solid #007bff;
            background: #f8f9fa;
            margin: 5px 0;
            font-family: monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <h1>🔍 语言下拉框实时检查器</h1>
    
    <div class="inspector">
        <h2>📊 页面元素检测状态</h2>
        <div id="detectionStatus"></div>
        
        <h3>🎯 目标元素位置</h3>
        <div class="code-block" id="elementInfo"></div>
        
        <h3>🎮 控制面板</h3>
        <button class="btn" onclick="detectElements()">重新检测元素</button>
        <button class="btn" onclick="inspectDropdown()">检查下拉框</button>
        <button class="btn" onclick="triggerDropdown()">触发下拉框</button>
        <button class="btn" onclick="showLogs()">显示日志</button>
        <button class="btn" onclick="clearLogs()">清除日志</button>
    </div>
    
    <div class="inspector">
        <h2>🎨 下拉框演示区域</h2>
        <p>如果在主页面中打开，下拉框应该显示在这里：</p>
        <div class="dropdown-demo">
            <iframe src="./index.html" width="100%" height="400" style="border:none; border-radius:4px;"></iframe>
        </div>
    </div>
    
    <div class="inspector">
        <h2>📝 实时监控日志</h2>
        <div id="logContainer"></div>
    </div>

    <script>
        let logs = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ timestamp, message, type });
            updateLogDisplay();
        }
        
        function updateLogDisplay() {
            const container = document.getElementById('logContainer');
            container.innerHTML = logs.map(log => 
                `<div class="log-entry">[${log.timestamp}] ${log.message}</div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }
        
        function detectElements() {
            addLog('🔍 开始检测页面元素...');
            
            // 尝试检测主页面中的元素
            const iframe = document.querySelector('iframe');
            let targetDoc = document;
            
            if (iframe && iframe.contentDocument) {
                targetDoc = iframe.contentDocument;
                addLog('📄 检测到iframe，使用iframe文档');
            }
            
            const elements = {
                container: targetDoc.querySelector('#languagesDropdown'),
                trigger: targetDoc.querySelector('#languagesTrigger'),
                options: targetDoc.querySelector('#languagesOptions'),
                hiddenSelect: targetDoc.querySelector('#languagesIdArray')
            };
            
            const statusDiv = document.getElementById('detectionStatus');
            const infoDiv = document.getElementById('elementInfo');
            
            let statusHTML = '';
            let infoHTML = '';
            let allFound = true;
            
            Object.entries(elements).forEach(([key, element]) => {
                const found = !!element;
                if (!found) allFound = false;
                
                statusHTML += `
                    <div class="status ${found ? 'success' : 'error'}">
                        ${found ? '✅' : '❌'} ${key}: ${found ? '已找到' : '未找到'}
                    </div>
                `;
                
                if (found) {
                    const rect = element.getBoundingClientRect();
                    infoHTML += `
${key}:
  - ID: ${element.id}
  - 类名: ${element.className}
  - 位置: (${Math.round(rect.x)}, ${Math.round(rect.y)})
  - 尺寸: ${Math.round(rect.width)} x ${Math.round(rect.height)}
  - 可见: ${rect.width > 0 && rect.height > 0 ? '是' : '否'}

`;
                    addLog(`✅ 找到 ${key}: #${element.id}`);
                } else {
                    addLog(`❌ 未找到 ${key}`);
                }
            });
            
            statusDiv.innerHTML = statusHTML;
            infoDiv.textContent = infoHTML;
            
            if (allFound) {
                addLog('🎉 所有必需元素都已找到！');
            } else {
                addLog('⚠️ 部分元素缺失，可能需要等待页面完全加载');
            }
        }
        
        function inspectDropdown() {
            addLog('🔍 检查下拉框详细状态...');
            
            const iframe = document.querySelector('iframe');
            const targetDoc = iframe && iframe.contentDocument ? iframe.contentDocument : document;
            
            const trigger = targetDoc.querySelector('#languagesTrigger');
            const options = targetDoc.querySelector('#languagesOptions');
            
            if (!trigger || !options) {
                addLog('❌ 无法找到下拉框元素');
                return;
            }
            
            const triggerStyle = window.getComputedStyle(trigger);
            const optionsStyle = window.getComputedStyle(options);
            
            addLog(`🎯 触发器状态:
  - aria-expanded: ${trigger.getAttribute('aria-expanded')}
  - display: ${triggerStyle.display}
  - visibility: ${triggerStyle.visibility}`);
            
            addLog(`📋 选项容器状态:
  - display: ${optionsStyle.display}
  - visibility: ${optionsStyle.visibility}
  - opacity: ${optionsStyle.opacity}
  - 子元素数量: ${options.children.length}`);
        }
        
        function triggerDropdown() {
            addLog('🎯 尝试触发下拉框...');
            
            const iframe = document.querySelector('iframe');
            const targetDoc = iframe && iframe.contentDocument ? iframe.contentDocument : document;
            
            const trigger = targetDoc.querySelector('#languagesTrigger');
            
            if (!trigger) {
                addLog('❌ 无法找到触发器元素');
                return;
            }
            
            // 模拟点击
            trigger.click();
            addLog('✅ 已触发点击事件');
            
            // 检查状态变化
            setTimeout(() => {
                const expanded = trigger.getAttribute('aria-expanded');
                addLog(`📊 点击后状态: aria-expanded = ${expanded}`);
                inspectDropdown();
            }, 100);
        }
        
        function showLogs() {
            console.log('📋 所有日志:', logs);
            addLog('📋 日志已输出到浏览器控制台');
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
            addLog('🧹 日志已清除');
        }
        
        // 页面加载完成后自动检测
        window.addEventListener('load', () => {
            addLog('🚀 页面检查器已启动');
            setTimeout(detectElements, 1000);
        });
        
        // 监听iframe加载
        window.addEventListener('message', (event) => {
            if (event.data.type === 'dropdownStateChange') {
                addLog(`📡 收到下拉框状态变化: ${JSON.stringify(event.data)}`);
            }
        });
    </script>
</body>
</html>