<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言数据调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 8px 15px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 语言数据调试工具</h1>
    
    <div class="debug-section">
        <h2>🚨 紧急修复状态</h2>
        <button onclick="testLanguageData()">测试语言数据获取</button>
        <button onclick="simulateFormManagerInit()">模拟FormManager初始化</button>
        <button onclick="simulateMultiOrderInit()">模拟MultiOrderManager初始化</button>
        <div id="testResults"></div>
    </div>

    <div class="debug-section">
        <h2>📊 数据源检查</h2>
        <div id="dataSourceCheck"></div>
    </div>

    <div class="debug-section">
        <h2>🛠️ 修复建议</h2>
        <div id="fixSuggestions"></div>
    </div>

    <script>
        // 模拟API服务数据
        const mockApiService = {
            staticData: {
                languages: [
                    { id: 2, name: 'English (EN)' },
                    { id: 3, name: 'Malay (MY)' },
                    { id: 4, name: 'Chinese (CN)' },
                    { id: 5, name: 'Paging (PG)' },
                    { id: 6, name: 'Charter (CHARTER)' },
                    { id: 8, name: '携程司导 (IM)' },
                    { id: 9, name: 'PSV (PSV)' },
                    { id: 10, name: 'EVP (EVP)' },
                    { id: 11, name: 'Car Type Reverify (CAR)' },
                    { id: 12, name: 'Jetty (JETTY)' }
                ]
            }
        };

        function testLanguageData() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>🧪 语言数据测试结果</h3>';

            // 测试1: 检查原始数据
            const originalData = mockApiService.staticData.languages;
            addResult(results, '原始数据检查', 'info', `原始语言数据: ${originalData.length} 项`);
            addResult(results, '数据详情', 'info', `<pre>${JSON.stringify(originalData, null, 2)}</pre>`);

            // 测试2: 测试去重逻辑
            const uniqueLanguages = originalData.reduce((acc, lang) => {
                if (!acc.find(existing => existing.id === lang.id)) {
                    acc.push(lang);
                }
                return acc;
            }, []);

            addResult(results, '去重测试', uniqueLanguages.length === originalData.length ? 'success' : 'warning', 
                `去重后: ${uniqueLanguages.length} 项 (原始: ${originalData.length} 项)`);

            // 测试3: 测试空数组情况
            const emptyArray = [];
            const deduplicatedEmpty = emptyArray.reduce((acc, lang) => {
                if (!acc.find(existing => existing.id === lang.id)) {
                    acc.push(lang);
                }
                return acc;
            }, []);

            addResult(results, '空数组测试', deduplicatedEmpty.length === 0 ? 'warning' : 'error', 
                `空数组去重后: ${deduplicatedEmpty.length} 项 - 这会导致语言列表消失!`);

            // 测试4: 测试备用数据逻辑
            let testLanguages = [];
            if (testLanguages.length === 0) {
                testLanguages.push(
                    { id: 2, name: 'English (EN)' },
                    { id: 3, name: 'Malay (MY)' },
                    { id: 4, name: 'Chinese (CN)' },
                    { id: 5, name: 'Paging Service' },
                    { id: 8, name: 'Ctrip Guide' }
                );
            }

            addResult(results, '备用数据测试', testLanguages.length > 0 ? 'success' : 'error', 
                `备用数据填充: ${testLanguages.length} 项`);
        }

        function simulateFormManagerInit() {
            const results = document.getElementById('testResults');
            addResult(results, 'FormManager模拟', 'info', '模拟FormManager初始化...');

            // 模拟FormManager的语言数据获取逻辑
            let languageData = [];
            
            // 步骤1: 尝试从mock API服务获取
            if (mockApiService && mockApiService.staticData && mockApiService.staticData.languages) {
                languageData = mockApiService.staticData.languages;
                addResult(results, 'API服务数据', 'success', `从API服务获取到 ${languageData.length} 个语言`);
            } else {
                addResult(results, 'API服务数据', 'error', 'API服务数据不可用');
            }

            // 步骤2: 去重处理
            console.log('🔍 [FormManager模拟] 原始语言数据:', languageData);
            
            const uniqueLanguages = languageData.reduce((acc, lang) => {
                if (!acc.find(existing => existing.id === lang.id)) {
                    acc.push(lang);
                }
                return acc;
            }, []);
            
            console.log('✅ [FormManager模拟] 去重后语言数据:', uniqueLanguages);
            
            // 步骤3: 备用数据检查
            if (uniqueLanguages.length === 0) {
                console.warn('⚠️ [FormManager模拟] 语言数据为空，使用紧急备用数据');
                uniqueLanguages.push(
                    { id: 2, name: 'English (EN)' },
                    { id: 3, name: 'Malay (MY)' },
                    { id: 4, name: 'Chinese (CN)' },
                    { id: 5, name: 'Paging Service' },
                    { id: 8, name: 'Ctrip Guide' }
                );
                addResult(results, 'FormManager备用数据', 'warning', `使用备用数据，最终获得 ${uniqueLanguages.length} 个语言`);
            } else {
                addResult(results, 'FormManager处理', 'success', `正常处理完成，最终获得 ${uniqueLanguages.length} 个语言`);
            }
        }

        function simulateMultiOrderInit() {
            const results = document.getElementById('testResults');
            addResult(results, 'MultiOrderManager模拟', 'info', '模拟MultiOrderManager初始化...');

            // 模拟相同的逻辑
            let languages = [];
            
            if (mockApiService && mockApiService.staticData && mockApiService.staticData.languages) {
                languages = mockApiService.staticData.languages;
                addResult(results, 'MultiOrder API数据', 'success', `从API服务获取到 ${languages.length} 个语言`);
            }

            const uniqueLanguages = languages.reduce((acc, lang) => {
                if (!acc.find(existing => existing.id === lang.id)) {
                    acc.push(lang);
                }
                return acc;
            }, []);

            if (uniqueLanguages.length === 0) {
                uniqueLanguages.push(
                    { id: 2, name: 'English (EN)' },
                    { id: 3, name: 'Malay (MY)' },
                    { id: 4, name: 'Chinese (CN)' },
                    { id: 5, name: 'Paging Service' },
                    { id: 8, name: 'Ctrip Guide' }
                );
                addResult(results, 'MultiOrder备用数据', 'warning', `使用备用数据，最终获得 ${uniqueLanguages.length} 个语言`);
            } else {
                addResult(results, 'MultiOrder处理', 'success', `正常处理完成，最终获得 ${uniqueLanguages.length} 个语言`);
            }
        }

        function addResult(container, title, type, message) {
            const div = document.createElement('div');
            div.className = `debug-section ${type}`;
            div.innerHTML = `<strong>${title}:</strong> ${message}`;
            container.appendChild(div);
        }

        // 页面加载时自动检查数据源
        window.addEventListener('load', () => {
            const dataCheck = document.getElementById('dataSourceCheck');
            
            dataCheck.innerHTML = `
                <h3>🔍 数据源状态检查</h3>
                <div class="debug-section info">
                    <strong>Mock API数据:</strong> ✅ 可用 (${mockApiService.staticData.languages.length} 个语言)
                </div>
                <div class="debug-section warning">
                    <strong>实际问题:</strong> 如果真实环境中API服务未正确初始化或语言数据为空，去重后仍然是空数组
                </div>
                <div class="debug-section info">
                    <strong>解决方案:</strong> 在去重后检查数组长度，如果为0则使用备用静态数据
                </div>
            `;

            const fixSuggestions = document.getElementById('fixSuggestions');
            fixSuggestions.innerHTML = `
                <h3>🛠️ 修复建议</h3>
                <div class="debug-section success">
                    <strong>✅ 已实施:</strong> 在FormManager和MultiOrderManager中添加了备用数据逻辑
                </div>
                <div class="debug-section info">
                    <strong>📋 后续步骤:</strong>
                    <ol>
                        <li>检查主页面中的控制台日志，确认数据获取状态</li>
                        <li>验证API服务是否正确初始化</li>
                        <li>确认AppState中的systemData是否正确设置</li>
                        <li>如有必要，调整备用数据的内容以匹配业务需求</li>
                    </ol>
                </div>
            `;
        });
    </script>
</body>
</html>