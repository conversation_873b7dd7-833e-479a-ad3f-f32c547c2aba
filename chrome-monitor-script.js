/**
 * Chrome开发者工具语言选择监控脚本
 * 用于监控语言下拉框的DOM变化和交互效果
 */

// 监控配置
const MONITOR_CONFIG = {
    targetSelector: '#languagesDropdown',
    triggerSelector: '#languagesTrigger', 
    optionsSelector: '#languagesOptions',
    hiddenSelectSelector: '#languagesIdArray',
    enableDOMObserver: true,
    enableEventTracking: true,
    enableStyleTracking: true,
    logLevel: 'debug' // debug, info, warn, error
};

// 监控数据收集器
class LanguageDropdownMonitor {
    constructor() {
        this.observers = [];
        this.eventListeners = [];
        this.stateHistory = [];
        this.styleChanges = [];
        this.domMutations = [];
        
        this.init();
    }
    
    init() {
        console.log('🔍 启动语言下拉框监控系统...');
        this.setupDOMObserver();
        this.setupEventTracking();
        this.setupStyleMonitoring();
        this.logInitialState();
    }
    
    /**
     * 记录初始状态
     */
    logInitialState() {
        const dropdown = document.querySelector(MONITOR_CONFIG.targetSelector);
        const trigger = document.querySelector(MONITOR_CONFIG.triggerSelector);
        const options = document.querySelector(MONITOR_CONFIG.optionsSelector);
        const hiddenSelect = document.querySelector(MONITOR_CONFIG.hiddenSelectSelector);
        
        console.group('📋 初始状态检查');
        
        if (dropdown) {
            console.log('✅ 下拉框容器存在:', dropdown);
            console.log('📊 容器位置信息:', dropdown.getBoundingClientRect());
            console.log('🎨 容器计算样式:', {
                display: getComputedStyle(dropdown).display,
                position: getComputedStyle(dropdown).position,
                visibility: getComputedStyle(dropdown).visibility,
                zIndex: getComputedStyle(dropdown).zIndex
            });
        } else {
            console.error('❌ 下拉框容器未找到');
        }
        
        if (trigger) {
            console.log('✅ 触发器存在:', trigger);
            console.log('🔧 触发器属性:', {
                'aria-expanded': trigger.getAttribute('aria-expanded'),
                'tabindex': trigger.getAttribute('tabindex'),
                'role': trigger.getAttribute('role')
            });
        } else {
            console.error('❌ 触发器未找到');
        }
        
        if (options) {
            console.log('✅ 选项容器存在:', options);
            console.log('📊 选项容器信息:', {
                childCount: options.children.length,
                display: getComputedStyle(options).display,
                visibility: getComputedStyle(options).visibility,
                opacity: getComputedStyle(options).opacity,
                maxHeight: getComputedStyle(options).maxHeight
            });
        } else {
            console.error('❌ 选项容器未找到');
        }
        
        if (hiddenSelect) {
            console.log('✅ 隐藏选择框存在:', hiddenSelect);
            console.log('📝 选项数量:', hiddenSelect.options.length);
            Array.from(hiddenSelect.options).forEach((option, index) => {
                console.log(`  选项 ${index}:`, {
                    value: option.value,
                    text: option.textContent,
                    selected: option.selected
                });
            });
        } else {
            console.error('❌ 隐藏选择框未找到');
        }
        
        console.groupEnd();
    }
    
    /**
     * 设置DOM变化观察器
     */
    setupDOMObserver() {
        if (!MONITOR_CONFIG.enableDOMObserver) return;
        
        const target = document.querySelector(MONITOR_CONFIG.targetSelector);
        if (!target) {
            console.warn('⚠️ 无法找到监控目标元素');
            return;
        }
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                const timestamp = new Date().toISOString();
                const mutationData = {
                    timestamp,
                    type: mutation.type,
                    target: mutation.target.tagName + (mutation.target.id ? `#${mutation.target.id}` : '') + (mutation.target.className ? `.${mutation.target.className.split(' ').join('.')}` : ''),
                    addedNodes: Array.from(mutation.addedNodes).map(node => node.nodeType === 1 ? node.tagName : node.textContent?.trim()).filter(Boolean),
                    removedNodes: Array.from(mutation.removedNodes).map(node => node.nodeType === 1 ? node.tagName : node.textContent?.trim()).filter(Boolean),
                    attributeName: mutation.attributeName,
                    oldValue: mutation.oldValue,
                    newValue: mutation.attributeName ? mutation.target.getAttribute(mutation.attributeName) : null
                };
                
                this.domMutations.push(mutationData);
                
                console.log('🔄 DOM变化检测:', mutationData);
                
                // 特别关注class变化
                if (mutation.attributeName === 'class') {
                    console.log('🎨 CSS类变化:', {
                        element: mutationData.target,
                        oldClass: mutation.oldValue,
                        newClass: mutationData.newValue,
                        classList: Array.from(mutation.target.classList)
                    });
                }
                
                // 特别关注aria-expanded变化
                if (mutation.attributeName === 'aria-expanded') {
                    console.log('♿ 可访问性属性变化:', {
                        element: mutationData.target,
                        expanded: mutationData.newValue === 'true'
                    });
                }
            });
        });
        
        observer.observe(target, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true,
            characterData: true,
            characterDataOldValue: true
        });
        
        this.observers.push(observer);
        console.log('👁️ DOM观察器已启动');
    }
    
    /**
     * 设置事件跟踪
     */
    setupEventTracking() {
        if (!MONITOR_CONFIG.enableEventTracking) return;
        
        const trigger = document.querySelector(MONITOR_CONFIG.triggerSelector);
        const options = document.querySelector(MONITOR_CONFIG.optionsSelector);
        
        if (!trigger) {
            console.warn('⚠️ 无法找到触发器元素进行事件监控');
            return;
        }
        
        // 监控的事件类型
        const events = ['click', 'mousedown', 'mouseup', 'keydown', 'keyup', 'focus', 'blur'];
        
        events.forEach(eventType => {
            const handler = (event) => {
                const timestamp = new Date().toISOString();
                const eventData = {
                    timestamp,
                    type: eventType,
                    target: event.target.tagName + (event.target.id ? `#${event.target.id}` : ''),
                    key: event.key,
                    code: event.code,
                    ctrlKey: event.ctrlKey,
                    shiftKey: event.shiftKey,
                    altKey: event.altKey,
                    clientX: event.clientX,
                    clientY: event.clientY
                };
                
                console.log(`🎯 事件触发 [${eventType}]:`, eventData);
                
                // 记录下拉框状态
                if (eventType === 'click') {
                    setTimeout(() => {
                        this.logDropdownState('点击后状态');
                    }, 100);
                }
            };
            
            trigger.addEventListener(eventType, handler);
            this.eventListeners.push({ element: trigger, type: eventType, handler });
        });
        
        // 监控选项容器的变化
        if (options) {
            const optionHandler = (event) => {
                console.log('📝 选项交互:', {
                    type: event.type,
                    target: event.target.tagName + (event.target.className ? `.${event.target.className}` : ''),
                    checked: event.target.checked,
                    value: event.target.value
                });
            };
            
            options.addEventListener('click', optionHandler);
            options.addEventListener('change', optionHandler);
            this.eventListeners.push({ element: options, type: 'click', handler: optionHandler });
            this.eventListeners.push({ element: options, type: 'change', handler: optionHandler });
        }
        
        console.log('🎮 事件监控已启动');
    }
    
    /**
     * 设置样式监控
     */
    setupStyleMonitoring() {
        if (!MONITOR_CONFIG.enableStyleTracking) return;
        
        const options = document.querySelector(MONITOR_CONFIG.optionsSelector);
        if (!options) return;
        
        // 监控样式变化
        const styleObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'style' || mutation.attributeName === 'class') {
                    const computedStyle = getComputedStyle(mutation.target);
                    const styleData = {
                        timestamp: new Date().toISOString(),
                        element: mutation.target.tagName + (mutation.target.id ? `#${mutation.target.id}` : ''),
                        display: computedStyle.display,
                        visibility: computedStyle.visibility,
                        opacity: computedStyle.opacity,
                        position: computedStyle.position,
                        top: computedStyle.top,
                        left: computedStyle.left,
                        width: computedStyle.width,
                        height: computedStyle.height,
                        zIndex: computedStyle.zIndex,
                        transform: computedStyle.transform
                    };
                    
                    this.styleChanges.push(styleData);
                    console.log('🎨 样式变化:', styleData);
                }
            });
        });
        
        styleObserver.observe(options, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });
        
        this.observers.push(styleObserver);
        console.log('🎨 样式监控已启动');
    }
    
    /**
     * 记录下拉框状态
     */
    logDropdownState(context = '') {
        const trigger = document.querySelector(MONITOR_CONFIG.triggerSelector);
        const options = document.querySelector(MONITOR_CONFIG.optionsSelector);
        const hiddenSelect = document.querySelector(MONITOR_CONFIG.hiddenSelectSelector);
        
        const state = {
            timestamp: new Date().toISOString(),
            context,
            trigger: trigger ? {
                'aria-expanded': trigger.getAttribute('aria-expanded'),
                classes: Array.from(trigger.classList),
                display: getComputedStyle(trigger).display,
                visibility: getComputedStyle(trigger).visibility
            } : null,
            options: options ? {
                classes: Array.from(options.classList),
                display: getComputedStyle(options).display,
                visibility: getComputedStyle(options).visibility,
                opacity: getComputedStyle(options).opacity,
                childCount: options.children.length,
                scrollHeight: options.scrollHeight,
                clientHeight: options.clientHeight
            } : null,
            hiddenSelect: hiddenSelect ? {
                selectedCount: Array.from(hiddenSelect.selectedOptions).length,
                selectedValues: Array.from(hiddenSelect.selectedOptions).map(opt => opt.value)
            } : null
        };
        
        this.stateHistory.push(state);
        
        console.group(`📊 下拉框状态 ${context ? `- ${context}` : ''}`);
        console.log('状态快照:', state);
        console.groupEnd();
        
        return state;
    }
    
    /**
     * 生成监控报告
     */
    generateReport() {
        console.group('📈 语言下拉框监控报告');
        
        console.log('📊 状态历史记录:', this.stateHistory);
        console.log('🔄 DOM变化记录:', this.domMutations);
        console.log('🎨 样式变化记录:', this.styleChanges);
        
        // 统计信息
        console.group('📈 统计信息');
        console.log(`总状态变化次数: ${this.stateHistory.length}`);
        console.log(`DOM变化次数: ${this.domMutations.length}`);
        console.log(`样式变化次数: ${this.styleChanges.length}`);
        console.groupEnd();
        
        console.groupEnd();
        
        return {
            stateHistory: this.stateHistory,
            domMutations: this.domMutations,
            styleChanges: this.styleChanges,
            summary: {
                stateChanges: this.stateHistory.length,
                domChanges: this.domMutations.length,
                styleChanges: this.styleChanges.length
            }
        };
    }
    
    /**
     * 手动触发下拉框
     */
    triggerDropdown() {
        const trigger = document.querySelector(MONITOR_CONFIG.triggerSelector);
        if (trigger) {
            console.log('🎯 手动触发下拉框点击');
            this.logDropdownState('触发前状态');
            trigger.click();
            setTimeout(() => {
                this.logDropdownState('触发后状态');
            }, 200);
        } else {
            console.error('❌ 无法找到触发器元素');
        }
    }
    
    /**
     * 清理监控器
     */
    cleanup() {
        // 移除观察器
        this.observers.forEach(observer => observer.disconnect());
        
        // 移除事件监听器
        this.eventListeners.forEach(({ element, type, handler }) => {
            element.removeEventListener(type, handler);
        });
        
        console.log('🧹 监控器已清理');
    }
}

// 自动启动监控器
let languageMonitor;

// 等待DOM加载完成
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        languageMonitor = new LanguageDropdownMonitor();
    });
} else {
    languageMonitor = new LanguageDropdownMonitor();
}

// 添加全局命令方便在控制台使用
window.languageMonitor = {
    getInstance: () => languageMonitor,
    trigger: () => languageMonitor?.triggerDropdown(),
    state: () => languageMonitor?.logDropdownState('手动检查'),
    report: () => languageMonitor?.generateReport(),
    cleanup: () => languageMonitor?.cleanup()
};

console.log(`
🎯 语言下拉框监控器已加载！

可用命令：
- languageMonitor.trigger()  // 手动触发下拉框
- languageMonitor.state()    // 查看当前状态  
- languageMonitor.report()   // 生成监控报告
- languageMonitor.cleanup()  // 清理监控器

现在可以手动点击语言选择下拉框，所有变化都会被记录到控制台。
`);