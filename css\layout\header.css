/**
 * 头部布局样式
 * 包含导航栏、标题、控制按钮等
 */

/* =================================
   应用头部
   ================================= */
.app-header {
  background: var(--bg-glass);
  backdrop-filter: var(--blur-glass);
  -webkit-backdrop-filter: var(--blur-glass);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--spacing-3) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--spacing-4);
  max-width: 100vw;
  margin: 0 auto;
  gap: var(--spacing-4);
  flex-wrap: wrap;
  min-width: 0;
}

/* =================================
   应用标题
   ================================= */
.app-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  min-width: 0;
  flex-shrink: 1;
}

.title-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}

/* =================================
   头部控制区
   ================================= */
.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
  min-width: 0;
}

.persistent-email {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.persistent-email input {
  width: 200px;
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* =================================
   主题切换
   ================================= */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.language-select {
  padding: var(--spacing-2);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  font-size: var(--font-size-sm);
  min-width: 80px;
}

/* =================================
   移动端响应式优化
   ================================= */
@media (max-width: 768px) {
  .app-header {
    padding: var(--spacing-2) 0;
  }
  
  .header-content {
    padding: 0 var(--spacing-2);
    gap: var(--spacing-2);
  }
  
  .app-title {
    font-size: var(--font-size-lg);
    gap: var(--spacing-1);
  }
  
  .title-icon {
    font-size: var(--font-size-xl);
  }
  
  .header-controls {
    gap: var(--spacing-2);
  }
  
  .persistent-email {
    display: none; /* 在移动端隐藏邮箱输入 */
  }
  
  .user-info {
    display: flex !important;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-1);
  }
  
  .language-select {
    min-width: 60px;
    padding: var(--spacing-1);
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-1);
  }
  
  .app-title {
    font-size: var(--font-size-base);
  }
  
  .title-icon {
    font-size: var(--font-size-lg);
  }
  
  .header-controls {
    gap: var(--spacing-1);
  }
  
  .user-info .btn {
    padding: var(--spacing-1);
    min-width: 32px;
  }
}