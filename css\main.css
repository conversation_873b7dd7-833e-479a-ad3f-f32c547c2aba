/**
 * 主CSS文件 - 导入所有模块化CSS文件
 * 按照优先级顺序导入：基础 -> 布局 -> 组件 -> 页面 -> 主题
 */

/* =================================
   基础层 - 变量、重置、工具类
   ================================= */
@import url('./base/variables.css');
@import url('./base/reset.css');
@import url('./base/utilities.css');

/* =================================
   布局层 - 网格、头部、容器
   ================================= */
@import url('./layout/grid.css');
@import url('./layout/header.css');

/* =================================
   组件层 - 可重用组件
   ================================= */
@import url('./components/buttons.css');
@import url('./components/forms.css');
@import url('./components/cards.css');

/* =================================
   页面层 - 特定页面样式
   ================================= */
@import url('./pages/workspace.css');

/* =================================
   现有模块兼容 - 保持向后兼容
   ================================= */
@import url('./multi-order-cards.css');

/* =================================
   全局应用样式
   ================================= */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* =================================
   过渡和动画增强
   ================================= */
* {
  transition: color var(--transition-fast), 
              background-color var(--transition-fast), 
              border-color var(--transition-fast),
              box-shadow var(--transition-fast);
}

/* 禁用拖拽时的过渡 */
.resizing * {
  transition: none !important;
}

/* =================================
   可访问性增强
   ================================= */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-primary: #000000;
    --text-secondary: #333333;
  }
}

/* 打印样式 */
@media print {
  * {
    background: var(--color-white) !important;
    color: var(--color-gray-900) !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
  
  .btn,
  .header-controls,
  .status-bar {
    display: none !important;
  }
}